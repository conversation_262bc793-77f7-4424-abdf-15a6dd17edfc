.notFoundContainer {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: var(--section-padding);
  padding-bottom: var(--section-padding);
}

.content {
  text-align: center;
  max-width: 600px;
  width: 100%;
  animation: fadeInUp 0.8s ease-out;

  h1 {
    margin-bottom: var(--gap-padding);
    color: #19271B;
    animation: slideInFromRight 1s ease-out 0.4s both;
  }

  p {
    margin-bottom: calc(var(--gap-padding) * 1.5);
    color: #666;
    animation: fadeIn 1s ease-out 0.6s both;
  }
}

.errorCode {
  font-size: clamp(4rem, 12vw, 8rem);
  font-weight: 700;
  color: #19271B;
  line-height: 1;
  margin-bottom: var(--gap-padding);
  opacity: 0.1;
  user-select: none;
  animation: slideInFromLeft 1s ease-out 0.2s both;
}

.buttonContainer {
  display: flex;
  justify-content: center;
  animation: fadeInUp 1s ease-out 0.8s both;
}

// Animations simples
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 0.1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// Animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 0.1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.05;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.1;
  }
}

// Responsive adjustments
@media screen and (max-width: 768px) {
  .notFoundContainer {
    padding: var(--gap-padding);
  }

  .content {
    padding: 2rem 0;
  }

  .errorCode {
    margin-bottom: 0.5rem;
  }

  .title {
    margin-bottom: 0.75rem;
  }

  .message {
    margin-bottom: 2rem;
  }
}

@media screen and (max-width: 480px) {
  .message {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }

  .buttonContainer {
    margin-top: 1.5rem;
  }

  .errorCode {
    font-size: clamp(3rem, 10vw, 6rem);
  }
}
