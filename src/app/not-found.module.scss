.notFoundContainer {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #FCFCF8;
  padding: var(--container-padding);
  position: relative;
  overflow: hidden;
}

.content {
  text-align: center;
  max-width: 600px;
  width: 100%;
  animation: fadeInUp 0.8s ease-out;
  position: relative;
  z-index: 2;
}

.errorCode {
  font-size: clamp(4rem, 12vw, 8rem);
  font-weight: 700;
  color: #19271B;
  line-height: 1;
  margin-bottom: 1rem;
  opacity: 0.1;
  user-select: none;
  animation: slideInFromLeft 1s ease-out 0.2s both;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120%;
    height: 120%;
    background: radial-gradient(circle, rgba(25, 39, 27, 0.05) 0%, transparent 70%);
    z-index: -1;
    animation: pulse 3s ease-in-out infinite;
  }
}

.title {
  font-size: clamp(2rem, 5vw, 3rem);
  font-weight: 500;
  color: #19271B;
  margin-bottom: 1rem;
  line-height: 1.2;
  animation: slideInFromRight 1s ease-out 0.4s both;
}

.message {
  font-size: clamp(1rem, 2.5vw, 1.25rem);
  color: #666;
  margin-bottom: 2.5rem;
  line-height: 1.5;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
  animation: fadeIn 1s ease-out 0.6s both;
}

.buttonContainer {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
  animation: fadeInUp 1s ease-out 0.8s both;
}

// Animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 0.1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.05;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.1;
  }
}

// Responsive adjustments
@media screen and (max-width: 768px) {
  .notFoundContainer {
    padding: var(--gap-padding);
  }

  .content {
    padding: 2rem 0;
  }

  .errorCode {
    margin-bottom: 0.5rem;
  }

  .title {
    margin-bottom: 0.75rem;
  }

  .message {
    margin-bottom: 2rem;
  }
}

@media screen and (max-width: 480px) {
  .message {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }

  .buttonContainer {
    margin-top: 1.5rem;
  }

  .errorCode {
    font-size: clamp(3rem, 10vw, 6rem);
  }
}
