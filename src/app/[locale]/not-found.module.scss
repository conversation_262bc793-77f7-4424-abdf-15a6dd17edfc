.notFoundContainer {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: var(--section-padding);
  padding-bottom: var(--section-padding);
}

.content {
  text-align: center;
  max-width: 600px;
  width: 100%;
  animation: fadeInUp 0.8s ease-out;
  
  h1 {
    margin-bottom: var(--gap-padding);
    color: #19271B;
    animation: slideInFromRight 1s ease-out 0.4s both;
  }
  
  p {
    margin-bottom: calc(var(--gap-padding) * 1.5);
    color: #666;
    animation: fadeIn 1s ease-out 0.6s both;
  }
}

.errorCode {
  font-size: clamp(4rem, 12vw, 8rem);
  font-weight: 700;
  color: #19271B;
  line-height: 1;
  margin-bottom: var(--gap-padding);
  opacity: 0.1;
  user-select: none;
  animation: slideInFromLeft 1s ease-out 0.2s both;
}

.buttonContainer {
  display: flex;
  justify-content: center;
  animation: fadeInUp 1s ease-out 0.8s both;
}

// Animations simples
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 0.1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
