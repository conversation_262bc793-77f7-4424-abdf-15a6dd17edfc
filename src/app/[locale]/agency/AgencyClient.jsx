"use client";

import Hero from '@/components/Heros/Hero';
import StatsCards from '@/components/StatsCards';
import ThreeColumnTitle from '@/components/ThreeColumnTitle';
import Description from '@/components/Description';
import ExpertiseAccordion from '@/components/ExpertiseAccordion';
import Testimonials from '@/components/Testimonials';
import { useTranslation } from "@/hooks/useTranslation";

export default function AgencyClient({ params }) {
  const { t } = useTranslation('pages');
  const locale = params.locale || 'fr';

  // Données des expertises (en attendant une meilleure solution de traduction)
  const expertisesData = locale === 'fr' ? [
    {
      title: "Création et refonte d'identité",
      items: [
        "Création de logotype",
        "Design de marchandises",
        "Impression sur-mesure"
      ]
    },
    {
      title: "Stratégie et design thinking",
      items: [
        "Analyse concurrentielle",
        "Définition de personas",
        "Parcours utilisateur"
      ]
    },
    {
      title: "Développement web",
      items: [
        "Sites vitrine",
        "E-commerce",
        "Applications web"
      ]
    }
  ] : [
    {
      title: "Brand identity creation and redesign",
      items: [
        "Logo creation",
        "Merchandise design",
        "Custom printing"
      ]
    },
    {
      title: "Strategy and design thinking",
      items: [
        "Competitive analysis",
        "Persona definition",
        "User journey"
      ]
    },
    {
      title: "Web development",
      items: [
        "Showcase websites",
        "E-commerce",
        "Web applications"
      ]
    }
  ];

  return (
    <div>
      <Hero
        title={t('agency.hero_title')}
        subtitle={t('agency.hero_subtitle')}
        locale={locale}
      />
      <StatsCards />
      <ThreeColumnTitle locale={locale} />

      {/* Séparateur */}
      <div className="container">
        <div style={{
          width: '100%',
          height: '1px',
          backgroundColor: '#000',
          margin: '0'
        }} />
      </div>

      <Description
        descriptionTitle={t('agency.values1.title')}
        descriptionText={t('agency.values1.text')}
        showButton={false}
        titleTag="h3"
      />

      {/* Séparateur */}
      <div className="container">
        <div style={{
          width: '100%',
          height: '1px',
          backgroundColor: '#000',
          margin: '0'
        }} />
      </div>

      <Description
        descriptionTitle={t('agency.values2.title')}
        descriptionText={t('agency.values2.text')}
        showButton={false}
        titleTag="h3"
      />

      {/* Séparateur */}
      <div className="container">
        <div style={{
          width: '100%',
          height: '1px',
          backgroundColor: '#000',
          margin: '0'
        }} />
      </div>

      <Description
        descriptionTitle={t('agency.values3.title')}
        descriptionText={t('agency.values3.text')}
        showButton={false}
        titleTag="h3"
      />

      <ExpertiseAccordion
        title={locale === 'fr' ? 'Nos expertises' : 'Our expertise'}
        expertises={expertisesData}
      />

      <Testimonials locale={locale} />
    </div>
  );
}
