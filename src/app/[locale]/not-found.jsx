// app/[locale]/not-found.jsx

"use client";

import '../globals.scss';
import Rounded from '@/common/RoundedButton';
import { useTranslation } from '@/hooks/useTranslation';
import { useParams } from 'next/navigation';
import styles from './not-found.module.scss';

export default function NotFound() {
  const { t } = useTranslation('pages');
  const params = useParams();
  const locale = params?.locale || 'fr';

  return (
    <div className={styles.notFoundContainer}>
      <div className="container">
        <div className={styles.content}>
          <div className={styles.errorCode}>404</div>
          <h1>{t('not_found.heading')}</h1>
          <p className="text-big">{t('not_found.message')}</p>

          <div className={styles.buttonContainer}>
            <Rounded href={`/${locale}`}>
              <p>{t('not_found.back_home')}</p>
            </Rounded>
          </div>
        </div>
      </div>
    </div>
  );
}
