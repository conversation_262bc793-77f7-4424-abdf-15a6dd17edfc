// app/not-found.jsx
"use client";

import './globals.scss';
import Link from 'next/link';

export default function GlobalNotFound() {
  return (
    <div style={{
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: '#FCFCF8',
      padding: '2rem'
    }}>
      <div style={{
        textAlign: 'center',
        maxWidth: '600px',
        width: '100%'
      }}>
        <div style={{
          fontSize: 'clamp(4rem, 12vw, 8rem)',
          fontWeight: '700',
          color: '#19271B',
          lineHeight: '1',
          marginBottom: '1rem',
          opacity: '0.1'
        }}>404</div>
        <h1 style={{
          color: '#19271B',
          marginBottom: '1rem'
        }}>Page introuvable</h1>
        <p style={{
          color: '#666',
          marginBottom: '2rem',
          fontSize: '1.125rem'
        }}>La page que vous recherchez n'existe pas.</p>

        <Link
          href="/fr"
          style={{
            display: 'inline-block',
            padding: '0.75rem 2rem',
            backgroundColor: '#19271B',
            color: 'white',
            textDecoration: 'none',
            borderRadius: '3rem',
            transition: 'all 0.2s ease'
          }}
        >
          Retourner à l'accueil
        </Link>
      </div>
    </div>
  );
}
