// app/not-found.jsx

"use client";

import './globals.scss';
import Rounded from '@/common/RoundedButton';
import styles from './not-found.module.scss';

export default function NotFound() {
  return (
    <div className={styles.notFoundContainer}>
      <div className={styles.content}>
        <div className={styles.errorCode}>404</div>
        <h1 className={styles.title}>Page introuvable</h1>
        <p className={styles.message}>La page que vous recherchez n’existe pas.</p>

        <div className={styles.buttonContainer}>
          <Rounded href="/">
            <p>Retourner à l’accueil</p>
          </Rounded>
        </div>
      </div>
    </div>
  );
}
  