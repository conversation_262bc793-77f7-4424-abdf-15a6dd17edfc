.container {
  /* styles globaux si nécessaire */
}

.wrapper {
  border-bottom: 1px solid rgb(201, 201, 201);
  overflow: hidden;
}

/* bouton question */
.questionContainer {
  width: 100%;
  text-align: left;
  padding: var(--gap-padding) 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 20px;
  font-family: inherit;
  background: transparent;
  border: none;
  cursor: pointer;
  color: #19271B;
  -webkit-tap-highlight-color: transparent;
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: translateZ(0); /* Force hardware acceleration */
  will-change: opacity, transform;
}

/* Contenu du header avec flèche */
.headerContent {
  display: flex;
  align-items: center;
  gap: var(--gap-padding);
  width: 100%;
}

/* Container pour la flèche personnalisée */
.arrowContainer {
  width: 38px;
  height: 36px;
  overflow: hidden; /* Cache la flèche quand elle sort */
  position: relative;
  transform: translateZ(0); /* Force hardware acceleration */
}

/* Flèche personnalisée */
.customArrow {
  width: 38px;
  height: 36px;
  transform: translateZ(0); /* Force hardware acceleration */
  will-change: transform, opacity;

  svg {
    width: 100%;
    height: 100%;
    transform: translateZ(0); /* Force hardware acceleration */
  }
}

/* texte de la question */
.questionContent {
  margin: 0;
  font-size: calc(1.2rem + 1.2vw);
  font-weight: 500;
  color: #000; // Noir par défaut
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: translateZ(0); /* Force hardware acceleration */
}

/* conteneur de la réponse */
.answerContainer {
  padding: 0;
  overflow: hidden;
  transform: translateZ(0); /* Force hardware acceleration */
  will-change: height, opacity;
}

/* texte de la réponse */
.answerContent {
  padding-bottom: var(--gap-padding);
  transform: translateZ(0); /* Force hardware acceleration */

  p {
    margin: 0.5rem 0;
    color: #666;
    transform: translateZ(0); /* Force hardware acceleration */

    &:first-child {
      margin-top: 0;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

/* sur mobile, ajuster l'alignement de l'icône */
@media screen and (max-width: 768px) {
  .material-symbols-outlined {
    align-self: flex-start;
    margin-top: 8px;
  }
}

.material-symbols-outlined {
  color: #19271B; // pour forcer la couleur de l'icône
}

// Ajustements pour tablettes et desktops
@media (min-width: 768px) {
  .questionContainer {
    padding: calc(var(--gap-padding) * 1.2) 0;
  }

  .questionContent {
    font-size: calc(1.4rem + 0.8vw);
  }
}
